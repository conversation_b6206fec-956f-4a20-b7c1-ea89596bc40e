import sys
import os
import subprocess
import traceback
from datetime import datetime
from PyQt6.QtWidgets import (
    QApplication,
    QMainWindow,
    QPushButton,
    QVBoxLayout,
    QHBoxLayout,
    QWidget,
    QLabel,
    QMessageBox,
    QProgressBar,
)
from PyQt6.QtCore import Qt, QSize, QTimer
from PyQt6.QtGui import QFont, QIcon


# 创建日志目录
LOG_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
os.makedirs(LOG_DIR, exist_ok=True)


def log_exception(exc_type, exc_value, exc_traceback):
    """记录未捕获的异常到日志文件"""
    # 获取当前时间作为日志文件名的一部分
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(LOG_DIR, f"error_{timestamp}.log")

    # 将异常信息写入日志文件
    with open(log_file, "w", encoding="utf-8") as f:
        f.write(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"异常类型: {exc_type.__name__}\n")
        f.write(f"异常信息: {exc_value}\n")
        f.write("详细堆栈:\n")
        traceback.print_exception(exc_type, exc_value, exc_traceback, file=f)

    # 同时打印到控制台
    print(f"发生错误: {exc_value}")
    print(f"详细信息已记录到: {log_file}")

    # 显示错误消息框
    if QApplication.instance():
        QMessageBox.critical(
            None,
            "程序错误",
            f"程序发生错误:\n{exc_value}\n\n详细信息已记录到:\n{log_file}",
        )

    # 返回False表示异常未被处理，系统将调用默认处理程序
    return False


def exception_handler(func):
    """装饰器：捕获函数执行过程中的异常"""

    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            error_traceback = traceback.format_exc()
            print(f"错误: {e}")
            print(error_traceback)

            # 记录到日志文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            log_file = os.path.join(LOG_DIR, f"error_{timestamp}.log")
            with open(log_file, "w", encoding="utf-8") as f:
                f.write(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"错误: {e}\n")
                f.write("详细堆栈:\n")
                f.write(error_traceback)

            # 显示错误消息
            QMessageBox.critical(
                None if not args else args[0],
                "操作错误",
                f"操作过程中发生错误:\n{e}\n\n详细信息已记录到:\n{log_file}",
            )

    return wrapper


class AllControlPanel(QMainWindow):
    def __init__(self):
        try:
            super().__init__()

            # 设置窗口标题和大小
            self.setWindowTitle("系统控制面板")
            self.setMinimumSize(400, 300)

            # 创建中央部件
            central_widget = QWidget()
            self.setCentralWidget(central_widget)

            # 创建主布局
            main_layout = QVBoxLayout(central_widget)
            main_layout.setContentsMargins(20, 20, 20, 20)
            main_layout.setSpacing(20)

            # 添加标题标签
            title_label = QLabel("系统控制面板")
            title_font = QFont()
            title_font.setPointSize(18)
            title_font.setBold(True)
            title_label.setFont(title_font)
            title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            main_layout.addWidget(title_label)

            # 添加按钮布局
            button_layout = QVBoxLayout()
            button_layout.setSpacing(15)
            main_layout.addLayout(button_layout)

            # 创建三个按钮
            self.update_button = self.create_button(
                "一键更新", "#4CAF50", self.on_update
            )
            self.start_button = self.create_button("一键启动", "#2196F3", self.on_start)
            self.close_button = self.create_button("一键关闭", "#F44336", self.on_close)

            # 添加按钮到布局
            button_layout.addWidget(self.update_button)
            button_layout.addWidget(self.start_button)
            button_layout.addWidget(self.close_button)

            # 添加进度条
            self.progress_bar = QProgressBar()
            self.progress_bar.setVisible(False)
            main_layout.addWidget(self.progress_bar)

            # 添加底部状态标签
            self.status_label = QLabel("就绪")
            self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            main_layout.addWidget(self.status_label)

            # 创建定时器用于模拟进度
            self.timer = QTimer()
            self.timer.timeout.connect(self.update_progress)
            self.progress_value = 0
        except Exception as e:
            # 捕获初始化过程中的异常
            error_traceback = traceback.format_exc()
            print(f"初始化错误: {e}")
            print(error_traceback)

            # 记录到日志文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            log_file = os.path.join(LOG_DIR, f"init_error_{timestamp}.log")
            with open(log_file, "w", encoding="utf-8") as f:
                f.write(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"初始化错误: {e}\n")
                f.write("详细堆栈:\n")
                f.write(error_traceback)

            # 显示错误消息并退出
            QMessageBox.critical(
                None,
                "初始化错误",
                f"程序初始化过程中发生错误:\n{e}\n\n详细信息已记录到:\n{log_file}",
            )
            sys.exit(1)

    def create_button(self, text, color, callback):
        """创建一个自定义样式的按钮"""
        try:
            button = QPushButton(text)

            # 设置按钮样式
            button.setStyleSheet(
                f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border-radius: 5px;
                    padding: 10px;
                    font-size: 16px;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background-color: {color}DD;
                }}
                QPushButton:pressed {{
                    background-color: {color}AA;
                }}
            """
            )

            # 设置按钮大小策略
            button.setMinimumHeight(50)

            # 连接按钮点击信号到回调函数
            button.clicked.connect(callback)

            return button
        except Exception as e:
            # 捕获创建按钮过程中的异常
            error_traceback = traceback.format_exc()
            print(f"创建按钮错误: {e}")
            print(error_traceback)
            raise  # 重新抛出异常，让上层函数处理

    def start_progress(self, message):
        """开始显示进度条"""
        try:
            self.status_label.setText(message)
            self.progress_bar.setVisible(True)
            self.progress_value = 0
            self.progress_bar.setValue(0)
            self.timer.start(50)  # 每50毫秒更新一次

            # 禁用按钮
            self.update_button.setEnabled(False)
            self.start_button.setEnabled(False)
            self.close_button.setEnabled(False)
        except Exception as e:
            error_traceback = traceback.format_exc()
            print(f"进度条错误: {e}")
            print(error_traceback)

            # 记录到日志文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            log_file = os.path.join(LOG_DIR, f"progress_error_{timestamp}.log")
            with open(log_file, "w", encoding="utf-8") as f:
                f.write(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"进度条错误: {e}\n")
                f.write("详细堆栈:\n")
                f.write(error_traceback)

            # 显示错误消息
            QMessageBox.critical(
                self,
                "操作错误",
                f"显示进度条时发生错误:\n{e}\n\n详细信息已记录到:\n{log_file}",
            )

    def update_progress(self):
        """更新进度条"""
        try:
            self.progress_value += 1
            self.progress_bar.setValue(self.progress_value)

            if self.progress_value >= 100:
                self.timer.stop()
                self.progress_bar.setVisible(False)

                # 重新启用按钮
                self.update_button.setEnabled(True)
                self.start_button.setEnabled(True)
                self.close_button.setEnabled(True)
        except Exception as e:
            self.timer.stop()  # 停止定时器
            error_traceback = traceback.format_exc()
            print(f"更新进度条错误: {e}")
            print(error_traceback)

            # 记录到日志文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            log_file = os.path.join(LOG_DIR, f"update_progress_error_{timestamp}.log")
            with open(log_file, "w", encoding="utf-8") as f:
                f.write(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"更新进度条错误: {e}\n")
                f.write("详细堆栈:\n")
                f.write(error_traceback)

    @exception_handler
    def on_update(self):
        """一键更新按钮点击处理"""
        self.start_progress("正在更新系统...")

        # 这里添加实际的更新逻辑
        # 例如：
        # subprocess.Popen(["python", "update_script.py"])

        # 模拟完成后的操作
        QTimer.singleShot(5000, lambda: self.status_label.setText("更新完成"))

    @exception_handler
    def on_start(self):
        """一键启动按钮点击处理"""
        self.start_progress("正在启动系统...")

        # 这里添加实际的启动逻辑
        # 例如：
        # subprocess.Popen(["python", "start_script.py"])

        # 模拟完成后的操作
        QTimer.singleShot(5000, lambda: self.status_label.setText("系统已启动"))

    @exception_handler
    def on_close(self):
        """一键关闭按钮点击处理"""
        reply = QMessageBox.question(
            self,
            "确认",
            "确定要关闭系统吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No,
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.start_progress("正在关闭系统...")

            # 这里添加实际的关闭逻辑
            # 例如：
            # subprocess.Popen(["python", "stop_script.py"])

            # 模拟完成后的操作
            QTimer.singleShot(5000, lambda: self.status_label.setText("系统已关闭"))


def main():
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(os.path.abspath(__file__)), exist_ok=True)

        # 设置全局异常处理器
        sys.excepthook = log_exception

        app = QApplication(sys.argv)

        # 设置应用程序样式
        app.setStyle("Fusion")

        window = AllControlPanel()
        window.show()

        sys.exit(app.exec())
    except Exception as e:
        # 捕获主函数中的异常
        error_traceback = traceback.format_exc()
        print(f"主程序错误: {e}")
        print(error_traceback)

        # 记录到日志文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = os.path.join(LOG_DIR, f"main_error_{timestamp}.log")
        with open(log_file, "w", encoding="utf-8") as f:
            f.write(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"主程序错误: {e}\n")
            f.write("详细堆栈:\n")
            f.write(error_traceback)

        # 如果GUI已经初始化，显示错误消息框
        if QApplication.instance():
            QMessageBox.critical(
                None,
                "程序错误",
                f"程序运行过程中发生错误:\n{e}\n\n详细信息已记录到:\n{log_file}",
            )
        else:
            # 如果GUI尚未初始化，只打印错误信息
            print(f"程序错误: {e}")
            print(f"详细信息已记录到: {log_file}")

        # 退出程序
        sys.exit(1)


if __name__ == "__main__":
    main()
